import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:camerawesome/camerawesome_plugin.dart' as awesome;
import 'package:path_provider/path_provider.dart';
import '../../../../core/di/injection_container.dart';
import '../../../../core/theme/app_theme.dart';
import '../bloc/camera_bloc.dart';
import '../bloc/camera_event.dart';
import '../bloc/camera_state.dart' as bloc_state;
import '../widgets/premium_camera_overlay.dart';
import '../widgets/premium_camera_controls.dart';
import '../widgets/premium_camera_settings.dart';

class CamerAwesomeScreen extends StatelessWidget {
  const CamerAwesomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<CameraBloc>()..add(InitializeCameraEvent()),
      child: const CamerAwesomeView(),
    );
  }
}

class CamerAwesomeView extends StatefulWidget {
  const CamerAwesomeView({super.key});

  @override
  State<CamerAwesomeView> createState() => _CamerAwesomeViewState();
}

class _CamerAwesomeViewState extends State<CamerAwesomeView>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _showSettings = false;
  bool _isAutoCapturing = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startEntryAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _slideController = AnimationController(
      duration: AppTheme.animationSlow,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: AppTheme.curveEmphasized),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _slideController,
        curve: AppTheme.curveEmphasized,
      ),
    );
  }

  void _startEntryAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _slideController.forward();
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryDarkColor,
      body: BlocConsumer<CameraBloc, bloc_state.CameraState>(
        listener: (context, state) {
          if (state is bloc_state.CameraError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppTheme.errorColor,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                ),
              ),
            );
          }

          if (state is bloc_state.CameraImageCaptured) {
            // Navigate to document processing screen
            Navigator.of(
              context,
            ).pushNamed('/document-processing', arguments: state.imageBytes);
          }
        },
        builder: (context, state) {
          return AnimatedBuilder(
            animation: Listenable.merge([_fadeAnimation, _slideAnimation]),
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Stack(
                    children: [
                      // Camera Preview
                      _buildCameraPreview(context, state),

                      // Premium Overlay
                      if (state is bloc_state.CameraReady)
                        PremiumCameraOverlay(
                          isAutoCapturing: _isAutoCapturing,
                          onAutoCapture: () {
                            setState(() {
                              _isAutoCapturing = !_isAutoCapturing;
                            });
                            // TODO: Implement auto capture logic
                          },
                        ),

                      // Top Controls
                      _buildTopControls(context, state),

                      // Bottom Controls
                      if (state is CameraReady || state is CameraCapturing)
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: PremiumCameraControls(
                            onCapture: () {
                              HapticFeedback.mediumImpact();
                              context.read<CameraBloc>().add(
                                CaptureImageEvent(),
                              );
                            },
                            onSwitchCamera: () {
                              HapticFeedback.lightImpact();
                              context.read<CameraBloc>().add(
                                SwitchCameraEvent(),
                              );
                            },
                            onToggleFlash: () {
                              HapticFeedback.lightImpact();
                              context.read<CameraBloc>().add(
                                ToggleFlashEvent(),
                              );
                            },
                            onOpenGallery: () {
                              HapticFeedback.lightImpact();
                              // TODO: Implement gallery opening
                            },
                            onShowSettings: () {
                              setState(() {
                                _showSettings = !_showSettings;
                              });
                            },
                            isCapturing: state is CameraCapturing,
                            isFlashOn:
                                state is CameraReady ? state.isFlashOn : false,
                          ),
                        ),

                      // Settings Panel
                      if (_showSettings && state is CameraReady)
                        PremiumCameraSettings(
                          zoom: state.zoom,
                          exposure: state.exposure,
                          onZoomChanged: (zoom) {
                            context.read<CameraBloc>().add(
                              SetZoomEvent(zoom: zoom),
                            );
                          },
                          onExposureChanged: (exposure) {
                            context.read<CameraBloc>().add(
                              SetExposureEvent(exposure: exposure),
                            );
                          },
                          onClose: () {
                            setState(() {
                              _showSettings = false;
                            });
                          },
                        ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildCameraPreview(BuildContext context, CameraState state) {
    if (state is CameraLoading) {
      return _buildLoadingState();
    }

    if (state is CameraReady || state is CameraCapturing) {
      return _buildCamerAwesome();
    }

    return _buildErrorState();
  }

  Widget _buildCamerAwesome() {
    return CameraAwesomeBuilder.awesome(
      saveConfig: SaveConfig.photoAndVideo(
        initialCaptureMode: CaptureMode.photo,
        photoPathBuilder: () async {
          final directory = await getTemporaryDirectory();
          return '${directory.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
        },
      ),
      sensorConfig: SensorConfig.single(
        sensor: Sensor.back,
        aspectRatio: CameraAspectRatios.ratio_4_3,
      ),
      enablePhysicalButton: true,
      filter: AwesomeFilter.none,
      onMediaTap: (mediaCapture) {
        // Handle media capture
        if (mediaCapture.captureRequest.when(
          single: (single) => single.file != null,
          multiple: (multiple) => false,
        )) {
          // Process captured image
          _processCapturedImage(mediaCapture);
        }
      },
      middleContentBuilder: (context, state) {
        return Container(); // Empty container for custom overlay
      },
      bottomActionsBuilder: (context, state) {
        return Container(); // Empty container for custom controls
      },
      topActionsBuilder: (context, state) {
        return Container(); // Empty container for custom top controls
      },
    );
  }

  void _processCapturedImage(MediaCapture mediaCapture) {
    // TODO: Process the captured image and convert to Uint8List
    // This will be implemented with proper image processing
  }

  Widget _buildLoadingState() {
    return Container(
      color: AppTheme.primaryDarkColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: AppTheme.spacing64,
              height: AppTheme.spacing64,
              decoration: BoxDecoration(
                color: AppTheme.glassColor,
                borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                border: Border.all(color: AppTheme.glassBorderColor, width: 1),
              ),
              child: const Center(
                child: CircularProgressIndicator(
                  color: AppTheme.brandPrimary,
                  strokeWidth: 3,
                ),
              ),
            ),
            const SizedBox(height: AppTheme.spacing24),
            Text(
              'Initializing Camera...',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppTheme.textOnPrimaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Container(
      color: AppTheme.primaryDarkColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.camera_alt_outlined,
              size: AppTheme.spacing64,
              color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: AppTheme.spacing24),
            Text(
              'Camera Error',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppTheme.textOnPrimaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacing8),
            Text(
              'Unable to access camera',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopControls(BuildContext context, CameraState state) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: SafeArea(
        child: Container(
          padding: const EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.primaryDarkColor.withValues(alpha: 0.8),
                AppTheme.primaryDarkColor.withValues(alpha: 0.0),
              ],
            ),
          ),
          child: Row(
            children: [
              // Back Button
              Container(
                decoration: BoxDecoration(
                  color: AppTheme.glassColor,
                  borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                  border: Border.all(
                    color: AppTheme.glassBorderColor,
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Navigator.of(context).pop();
                  },
                  icon: const Icon(
                    Icons.arrow_back_rounded,
                    color: AppTheme.textOnPrimaryColor,
                  ),
                ),
              ),

              const Spacer(),

              // Camera Mode Indicator
              if (state is CameraReady)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacing16,
                    vertical: AppTheme.spacing8,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.glassColor,
                    borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                    border: Border.all(
                      color: AppTheme.glassBorderColor,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    'DOCUMENT',
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: AppTheme.textOnPrimaryColor,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
