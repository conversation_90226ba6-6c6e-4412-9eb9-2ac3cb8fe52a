import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumCameraControls extends StatefulWidget {
  final VoidCallback onCapture;
  final VoidCallback onSwitchCamera;
  final VoidCallback onToggleFlash;
  final VoidCallback onOpenGallery;
  final VoidCallback onShowSettings;
  final bool isCapturing;
  final bool isFlashOn;

  const PremiumCameraControls({
    super.key,
    required this.onCapture,
    required this.onSwitchCamera,
    required this.onToggleFlash,
    required this.onOpenGallery,
    required this.onShowSettings,
    required this.isCapturing,
    required this.isFlashOn,
  });

  @override
  State<PremiumCameraControls> createState() => _PremiumCameraControlsState();
}

class _PremiumCameraControlsState extends State<PremiumCameraControls>
    with TickerProviderStateMixin {
  late AnimationController _captureController;
  late AnimationController _pulseController;
  late Animation<double> _captureAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _captureController = AnimationController(
      duration: AppTheme.animationFast,
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _captureAnimation = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(
        parent: _captureController,
        curve: AppTheme.curveStandard,
      ),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    if (widget.isCapturing) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(PremiumCameraControls oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isCapturing != oldWidget.isCapturing) {
      if (widget.isCapturing) {
        _pulseController.repeat(reverse: true);
      } else {
        _pulseController.stop();
        _pulseController.reset();
      }
    }
  }

  @override
  void dispose() {
    _captureController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        padding: EdgeInsets.only(
          left: AppTheme.spacing24,
          right: AppTheme.spacing24,
          bottom: MediaQuery.of(context).padding.bottom + AppTheme.spacing24,
          top: AppTheme.spacing32,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.transparent,
              AppTheme.primaryDarkColor.withValues(alpha: 0.8),
              AppTheme.primaryDarkColor,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Top Controls
            _buildTopControls(),

            const SizedBox(height: AppTheme.spacing40),

            // Main Controls
            _buildMainControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildTopControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildControlButton(
          icon: Icons.close_rounded,
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
        Row(
          children: [
            _buildControlButton(
              icon:
                  widget.isFlashOn
                      ? Icons.flash_on_rounded
                      : Icons.flash_off_rounded,
              onPressed: widget.onToggleFlash,
              isActive: widget.isFlashOn,
              tooltip: 'Flash',
            ),
            const SizedBox(width: AppTheme.spacing16),
            _buildControlButton(
              icon: Icons.tune_rounded,
              onPressed: widget.onShowSettings,
              tooltip: 'Settings',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMainControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Gallery Button
        _buildSecondaryButton(
          icon: Icons.photo_library_rounded,
          onPressed: widget.onOpenGallery,
          tooltip: 'Gallery',
        ),

        // Capture Button
        _buildCaptureButton(),

        // Switch Camera Button
        _buildSecondaryButton(
          icon: Icons.flip_camera_ios_rounded,
          onPressed: widget.onSwitchCamera,
          tooltip: 'Switch Camera',
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    bool isActive = false,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          onPressed();
        },
        child: Container(
          width: AppTheme.spacing48,
          height: AppTheme.spacing48,
          decoration: BoxDecoration(
            color:
                isActive
                    ? AppTheme.glassColor
                    : AppTheme.primaryDarkColor.withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(AppTheme.radiusLG),
            border: Border.all(
              color:
                  isActive
                      ? AppTheme.glassBorderColor
                      : AppTheme.textOnPrimaryColor.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryDarkColor.withValues(alpha: 0.5),
                blurRadius: AppTheme.shadowBlurMD,
                offset: const Offset(0, AppTheme.spacing4),
              ),
            ],
          ),
          child: Icon(
            icon,
            color:
                isActive ? AppTheme.brandPrimary : AppTheme.textOnPrimaryColor,
            size: AppTheme.spacing24,
          ),
        ),
      ),
    );
  }

  Widget _buildSecondaryButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: GestureDetector(
        onTap: onPressed,
        child: Container(
          width: AppTheme.spacing56,
          height: AppTheme.spacing56,
          decoration: BoxDecoration(
            color: AppTheme.glassColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLG),
            border: Border.all(color: AppTheme.glassBorderColor, width: 1),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryDarkColor.withValues(alpha: 0.5),
                blurRadius: AppTheme.shadowBlurMD,
                offset: const Offset(0, AppTheme.spacing4),
              ),
            ],
          ),
          child: Icon(
            icon,
            color: AppTheme.textOnPrimaryColor,
            size: AppTheme.spacing28,
          ),
        ),
      ),
    );
  }

  Widget _buildCaptureButton() {
    return GestureDetector(
      onTapDown: (_) => _captureController.forward(),
      onTapUp: (_) {
        _captureController.reverse();
        if (!widget.isCapturing) {
          widget.onCapture();
        }
      },
      onTapCancel: () => _captureController.reverse(),
      child: AnimatedBuilder(
        animation: Listenable.merge([_captureAnimation, _pulseAnimation]),
        builder: (context, child) {
          final scale =
              _captureAnimation.value *
              (widget.isCapturing ? _pulseAnimation.value : 1.0);

          return Transform.scale(
            scale: scale,
            child: Container(
              width: AppTheme.spacing80,
              height: AppTheme.spacing80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors:
                      widget.isCapturing
                          ? AppTheme.accentGradient
                          : AppTheme.primaryGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: (widget.isCapturing
                            ? AppTheme.accentColor
                            : AppTheme.brandPrimary)
                        .withValues(alpha: 0.5),
                    blurRadius: AppTheme.shadowBlurLG,
                    offset: const Offset(0, AppTheme.spacing8),
                  ),
                  BoxShadow(
                    color: (widget.isCapturing
                            ? AppTheme.accentColor
                            : AppTheme.brandPrimary)
                        .withValues(alpha: 0.3),
                    blurRadius: AppTheme.shadowBlur2XL,
                    offset: const Offset(0, AppTheme.spacing16),
                  ),
                ],
              ),
              child: Container(
                margin: const EdgeInsets.all(AppTheme.spacing6),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppTheme.glassColor,
                  border: Border.all(
                    color: AppTheme.glassBorderColor,
                    width: 2,
                  ),
                ),
                child:
                    widget.isCapturing
                        ? Center(
                          child: SizedBox(
                            width: AppTheme.spacing24,
                            height: AppTheme.spacing24,
                            child: CircularProgressIndicator(
                              strokeWidth: 3,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppTheme.textOnPrimaryColor,
                              ),
                            ),
                          ),
                        )
                        : Icon(
                          Icons.camera_alt_rounded,
                          color: AppTheme.textOnPrimaryColor,
                          size: AppTheme.spacing32,
                        ),
              ),
            ),
          );
        },
      ),
    );
  }
}
