import 'package:equatable/equatable.dart';
import 'dart:typed_data';
import '../../domain/repositories/camera_repository.dart';

abstract class CameraState extends Equatable {
  const CameraState();

  @override
  List<Object?> get props => [];
}

class <PERSON><PERSON>nitial extends CameraState {}

class CameraLoading extends CameraState {}

class CameraReady extends CameraState {
  final bool isFlashOn;
  final CameraFlashMode flashMode;
  final CameraSensor currentSensor;
  final double zoom;
  final double exposure;
  final bool isAutoCapturing;

  const CameraReady({
    this.isFlashOn = false,
    this.flashMode = CameraFlashMode.off,
    this.currentSensor = CameraSensor.back,
    this.zoom = 1.0,
    this.exposure = 0.0,
    this.isAutoCapturing = false,
  });

  @override
  List<Object?> get props => [
    isFlashOn,
    flashMode,
    currentSensor,
    zoom,
    exposure,
    isAutoCapturing,
  ];

  CameraReady copyWith({
    bool? isFlashOn,
    CameraFlashMode? flashMode,
    CameraSensor? currentSensor,
    double? zoom,
    double? exposure,
    bool? isAutoCapturing,
  }) {
    return CameraReady(
      isFlashOn: isFlashOn ?? this.isFlashOn,
      flashMode: flashMode ?? this.flashMode,
      currentSensor: currentSensor ?? this.currentSensor,
      zoom: zoom ?? this.zoom,
      exposure: exposure ?? this.exposure,
      isAutoCapturing: isAutoCapturing ?? this.isAutoCapturing,
    );
  }
}

class CameraCapturing extends CameraState {
  const CameraCapturing();

  @override
  List<Object?> get props => [];
}

class CameraImageCaptured extends CameraState {
  final Uint8List imageBytes;

  const CameraImageCaptured({required this.imageBytes});

  @override
  List<Object?> get props => [imageBytes];
}

class CameraError extends CameraState {
  final String message;

  const CameraError({required this.message});

  @override
  List<Object?> get props => [message];
}
