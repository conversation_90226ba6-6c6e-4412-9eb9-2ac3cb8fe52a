import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

enum DocumentMode {
  auto('Auto'),
  document('Document'),
  id('ID Card'),
  receipt('Receipt'),
  whiteboard('Whiteboard'),
  book('Book');

  const DocumentMode(this.label);
  final String label;
}

/// Document scanning mode selector similar to Photo/Video mode in native cameras
class DocumentModeSelector extends StatefulWidget {
  final DocumentMode selectedMode;
  final Function(DocumentMode) onModeChanged;

  const DocumentModeSelector({
    super.key,
    required this.selectedMode,
    required this.onModeChanged,
  });

  @override
  State<DocumentModeSelector> createState() => _DocumentModeSelectorState();
}

class _DocumentModeSelectorState extends State<DocumentModeSelector> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 24),
        itemCount: DocumentMode.values.length,
        itemBuilder: (context, index) {
          final mode = DocumentMode.values[index];
          final isSelected = mode == widget.selectedMode;

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                widget.onModeChanged(mode);
                _scrollToSelected(index);
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border:
                      isSelected
                          ? null
                          : Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                ),
                child: Center(
                  child: Text(
                    mode.label,
                    style: TextStyle(
                      color: isSelected ? Colors.black : Colors.white,
                      fontSize: 14,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _scrollToSelected(int index) {
    if (_scrollController.hasClients) {
      final itemWidth = 100.0; // Approximate item width
      final targetOffset =
          (index * itemWidth) -
          (MediaQuery.of(context).size.width / 2) +
          (itemWidth / 2);

      _scrollController.animateTo(
        targetOffset.clamp(0.0, _scrollController.position.maxScrollExtent),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
}
