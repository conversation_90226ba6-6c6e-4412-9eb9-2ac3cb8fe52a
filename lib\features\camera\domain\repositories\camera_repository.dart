import 'dart:typed_data';
import 'package:flutter/material.dart';

enum CameraFlashMode { off, on, auto, torch }

enum CameraSensor { back, front }

abstract class CameraRepository {
  Future<bool> initializeCamera();
  Future<Uint8List> captureImage();
  Future<void> disposeCamera();
  Future<void> setFlashMode(CameraFlashMode flashMode);
  Future<void> switchCamera();
  Future<void> setZoom(double zoom);
  Future<void> setExposure(double exposure);
  Future<void> setFocus(Offset point);
  Future<void> startAutoCapture();
  Future<void> stopAutoCapture();
  Stream<CameraAwesomeState> get cameraStateStream;
  Stream<bool> get documentDetectionStream;
}

enum CameraAwesomeState { initializing, ready, capturing, error }
