import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Native-style camera controls that mimic iOS/Android built-in camera apps
class NativeCameraControls extends StatefulWidget {
  final VoidCallback onCapture;
  final VoidCallback onSwitchCamera;
  final VoidCallback onToggleFlash;
  final VoidCallback onOpenGallery;
  final VoidCallback onShowSettings;
  final bool isCapturing;
  final bool isFlashOn;

  const NativeCameraControls({
    super.key,
    required this.onCapture,
    required this.onSwitchCamera,
    required this.onToggleFlash,
    required this.onOpenGallery,
    required this.onShowSettings,
    required this.isCapturing,
    required this.isFlashOn,
  });

  @override
  State<NativeCameraControls> createState() => _NativeCameraControlsState();
}

class _NativeCameraControlsState extends State<NativeCameraControls>
    with TickerProviderStateMixin {
  late AnimationController _captureController;
  late Animation<double> _captureAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _captureController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _captureAnimation = Tween<double>(begin: 1.0, end: 0.85).animate(
      CurvedAnimation(parent: _captureController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _captureController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 24,
        right: 24,
        bottom: MediaQuery.of(context).padding.bottom + 32,
        top: 24,
      ),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.transparent, Color(0x80000000), Color(0xCC000000)],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Gallery Button
          _buildSecondaryButton(
            icon: Icons.photo_library_outlined,
            onPressed: widget.onOpenGallery,
          ),

          // Capture Button
          _buildCaptureButton(),

          // Switch Camera Button
          _buildSecondaryButton(
            icon: Icons.flip_camera_ios_outlined,
            onPressed: widget.onSwitchCamera,
          ),
        ],
      ),
    );
  }

  Widget _buildSecondaryButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onPressed();
      },
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Icon(icon, color: Colors.white, size: 24),
      ),
    );
  }

  Widget _buildCaptureButton() {
    return GestureDetector(
      onTapDown: (_) {
        HapticFeedback.mediumImpact();
        _captureController.forward();
      },
      onTapUp: (_) {
        _captureController.reverse();
        if (!widget.isCapturing) {
          widget.onCapture();
        }
      },
      onTapCancel: () => _captureController.reverse(),
      child: AnimatedBuilder(
        animation: _captureAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _captureAnimation.value,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                border: Border.all(color: Colors.white, width: 4),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child:
                  widget.isCapturing
                      ? Container(
                        margin: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                      )
                      : Container(
                        margin: const EdgeInsets.all(6),
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
            ),
          );
        },
      ),
    );
  }
}
