import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Native-style camera top bar with flash, timer, and settings controls
class NativeCameraTopBar extends StatelessWidget {
  final bool isFlashOn;
  final VoidCallback onToggleFlash;
  final VoidCallback onShowSettings;
  final VoidCallback onClose;

  const NativeCameraTopBar({
    super.key,
    required this.isFlashOn,
    required this.onToggleFlash,
    required this.onShowSettings,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: MediaQuery.of(context).padding.top + 8,
        bottom: 8,
      ),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xCC000000), Color(0x80000000), Colors.transparent],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Close Button
          _buildTopBarButton(icon: Icons.close, onPressed: onClose),

          // Center Controls
          Row(
            children: [
              // Flash Control
              _buildTopBarButton(
                icon: isFlashOn ? Icons.flash_on : Icons.flash_off,
                onPressed: onToggleFlash,
                isActive: isFlashOn,
              ),
              const SizedBox(width: 16),

              // Timer (placeholder for future implementation)
              _buildTopBarButton(
                icon: Icons.timer_outlined,
                onPressed: () {
                  // TODO: Implement timer functionality
                  HapticFeedback.lightImpact();
                },
              ),
            ],
          ),

          // Settings Button
          _buildTopBarButton(icon: Icons.tune, onPressed: onShowSettings),
        ],
      ),
    );
  }

  Widget _buildTopBarButton({
    required IconData icon,
    required VoidCallback onPressed,
    bool isActive = false,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onPressed();
      },
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color:
              isActive
                  ? Colors.yellow.withValues(alpha: 0.2)
                  : Colors.white.withValues(alpha: 0.1),
          shape: BoxShape.circle,
          border: isActive ? Border.all(color: Colors.yellow, width: 1) : null,
        ),
        child: Icon(
          icon,
          color: isActive ? Colors.yellow : Colors.white,
          size: 22,
        ),
      ),
    );
  }
}
