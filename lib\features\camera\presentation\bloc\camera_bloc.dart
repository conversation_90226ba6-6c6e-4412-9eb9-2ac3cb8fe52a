import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/initialize_camera.dart';
import '../../domain/usecases/capture_image.dart';
import '../../domain/repositories/camera_repository.dart';
import 'camera_event.dart';
import 'camera_state.dart';

class CameraBloc extends Bloc<CameraEvent, CameraState> {
  final InitializeCamera initializeCamera;
  final CaptureImage captureImage;
  final CameraRepository repository;

  bool _isInitialized = false;

  CameraBloc({
    required this.initializeCamera,
    required this.captureImage,
    required this.repository,
  }) : super(CameraInitial()) {
    on<InitializeCameraEvent>(_onInitializeCamera);
    on<CaptureImageEvent>(_onCaptureImage);
    on<DisposeCameraEvent>(_onDisposeCamera);
    on<ToggleFlashEvent>(_onToggleFlash);
    on<SwitchCameraEvent>(_onSwitchCamera);
    on<SetZoomEvent>(_onSetZoom);
    on<SetExposureEvent>(_onSetExposure);
    on<SetFocusEvent>(_onSetFocus);
  }

  Future<void> _onInitializeCamera(
    InitializeCameraEvent event,
    Emitter<CameraState> emit,
  ) async {
    try {
      emit(CameraLoading());

      final success = await initializeCamera();

      if (success) {
        _isInitialized = true;
        emit(const CameraReady());
      } else {
        emit(const CameraError(message: 'Failed to initialize camera'));
      }
    } catch (e) {
      emit(CameraError(message: e.toString()));
    }
  }

  Future<void> _onCaptureImage(
    CaptureImageEvent event,
    Emitter<CameraState> emit,
  ) async {
    if (!_isInitialized) {
      emit(const CameraError(message: 'Camera not initialized'));
      return;
    }

    try {
      emit(const CameraCapturing());

      final imageBytes = await captureImage();

      emit(CameraImageCaptured(imageBytes: imageBytes));
    } catch (e) {
      emit(CameraError(message: e.toString()));
    }
  }

  Future<void> _onDisposeCamera(
    DisposeCameraEvent event,
    Emitter<CameraState> emit,
  ) async {
    await repository.disposeCamera();
    _isInitialized = false;
    emit(CameraInitial());
  }

  Future<void> _onToggleFlash(
    ToggleFlashEvent event,
    Emitter<CameraState> emit,
  ) async {
    if (!_isInitialized) {
      return;
    }

    try {
      final currentState = state;
      if (currentState is CameraReady) {
        final newFlashMode =
            currentState.isFlashOn
                ? CameraFlashMode.off
                : CameraFlashMode.torch;

        await repository.setFlashMode(newFlashMode);

        emit(
          currentState.copyWith(
            isFlashOn: !currentState.isFlashOn,
            flashMode: newFlashMode,
          ),
        );
      }
    } catch (e) {
      emit(CameraError(message: e.toString()));
    }
  }

  Future<void> _onSwitchCamera(
    SwitchCameraEvent event,
    Emitter<CameraState> emit,
  ) async {
    if (!_isInitialized) return;

    try {
      await repository.switchCamera();

      final currentState = state;
      if (currentState is CameraReady) {
        final newSensor =
            currentState.currentSensor == CameraSensor.back
                ? CameraSensor.front
                : CameraSensor.back;

        emit(currentState.copyWith(currentSensor: newSensor));
      }
    } catch (e) {
      emit(CameraError(message: e.toString()));
    }
  }

  Future<void> _onSetZoom(SetZoomEvent event, Emitter<CameraState> emit) async {
    if (!_isInitialized) return;

    try {
      await repository.setZoom(event.zoom);

      final currentState = state;
      if (currentState is CameraReady) {
        emit(currentState.copyWith(zoom: event.zoom));
      }
    } catch (e) {
      emit(CameraError(message: e.toString()));
    }
  }

  Future<void> _onSetExposure(
    SetExposureEvent event,
    Emitter<CameraState> emit,
  ) async {
    if (!_isInitialized) return;

    try {
      await repository.setExposure(event.exposure);

      final currentState = state;
      if (currentState is CameraReady) {
        emit(currentState.copyWith(exposure: event.exposure));
      }
    } catch (e) {
      emit(CameraError(message: e.toString()));
    }
  }

  Future<void> _onSetFocus(
    SetFocusEvent event,
    Emitter<CameraState> emit,
  ) async {
    if (!_isInitialized) return;

    try {
      await repository.setFocus(event.point);
    } catch (e) {
      emit(CameraError(message: e.toString()));
    }
  }

  @override
  Future<void> close() {
    repository.disposeCamera();
    return super.close();
  }
}
