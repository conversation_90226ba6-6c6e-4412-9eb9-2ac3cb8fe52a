import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/di/injection_container.dart';
import '../../data/repositories/camera_repository_impl.dart';
import '../bloc/camera_bloc.dart';
import '../bloc/camera_event.dart';
import '../bloc/camera_state.dart';
import '../widgets/native_camera_controls.dart';
import '../widgets/native_camera_top_bar.dart';
import '../widgets/native_document_overlay.dart';
import '../widgets/native_camera_settings.dart';
import '../widgets/document_mode_selector.dart';
import '../widgets/camera_preview_widget.dart';

/// Native-style camera screen that mimics iOS/Android camera apps
class NativeCameraScreen extends StatelessWidget {
  const NativeCameraScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<CameraBloc>()..add(InitializeCameraEvent()),
      child: const NativeCameraView(),
    );
  }
}

class NativeCameraView extends StatefulWidget {
  const NativeCameraView({super.key});

  @override
  State<NativeCameraView> createState() => _NativeCameraViewState();
}

class _NativeCameraViewState extends State<NativeCameraView> {
  bool _showSettings = false;
  bool _gridEnabled = false;
  bool _autoFocusEnabled = true;
  bool _isAutoCapturing = false;
  double _currentZoom = 1.0;
  double _currentExposure = 0.0;
  DocumentMode _selectedMode = DocumentMode.auto;

  @override
  void initState() {
    super.initState();
    _setSystemUIOverlay();
  }

  @override
  void dispose() {
    _resetSystemUIOverlay();
    super.dispose();
  }

  void _setSystemUIOverlay() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
  }

  void _resetSystemUIOverlay() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  void _captureImage() {
    HapticFeedback.mediumImpact();
    context.read<CameraBloc>().add(CaptureImageEvent());
  }

  void _toggleFlash() {
    HapticFeedback.lightImpact();
    context.read<CameraBloc>().add(ToggleFlashEvent());
  }

  void _toggleSettings() {
    setState(() {
      _showSettings = !_showSettings;
    });
    HapticFeedback.lightImpact();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: BlocConsumer<CameraBloc, CameraState>(
        listener: (context, state) {
          if (state is CameraError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            );
          }

          if (state is CameraImageCaptured) {
            // Navigate to document processing screen
            Navigator.of(
              context,
            ).pushNamed('/document-processing', arguments: state.imageBytes);
          }
        },
        builder: (context, state) {
          return Stack(
            children: [
              // Camera Preview
              _buildCameraPreview(state),

              // Document Detection Overlay
              NativeDocumentOverlay(
                isAutoCapturing: _isAutoCapturing,
                showGrid: _gridEnabled,
                selectedMode: _selectedMode,
                onAutoCapture: () {
                  setState(() {
                    _isAutoCapturing = !_isAutoCapturing;
                  });
                  HapticFeedback.lightImpact();
                },
                onModeChanged: (mode) {
                  setState(() {
                    _selectedMode = mode;
                  });
                },
              ),

              // Top Bar
              NativeCameraTopBar(
                isFlashOn: state is CameraReady ? state.isFlashOn : false,
                onToggleFlash: _toggleFlash,
                onShowSettings: _toggleSettings,
                onClose: () => Navigator.of(context).pop(),
              ),

              // Bottom Controls
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: NativeCameraControls(
                  onCapture: _captureImage,
                  onSwitchCamera: () {
                    HapticFeedback.lightImpact();
                    context.read<CameraBloc>().add(SwitchCameraEvent());
                  },
                  onToggleFlash: _toggleFlash,
                  onOpenGallery: () {
                    // TODO: Implement gallery opening
                    HapticFeedback.lightImpact();
                  },
                  onShowSettings: _toggleSettings,
                  isCapturing: state is CameraCapturing,
                  isFlashOn: state is CameraReady ? state.isFlashOn : false,
                ),
              ),

              // Settings Panel
              if (_showSettings)
                NativeCameraSettings(
                  zoom: _currentZoom,
                  exposure: _currentExposure,
                  gridEnabled: _gridEnabled,
                  autoFocusEnabled: _autoFocusEnabled,
                  onZoomChanged: (zoom) {
                    setState(() {
                      _currentZoom = zoom;
                    });
                    context.read<CameraBloc>().add(SetZoomEvent(zoom: zoom));
                  },
                  onExposureChanged: (exposure) {
                    setState(() {
                      _currentExposure = exposure;
                    });
                    context.read<CameraBloc>().add(
                      SetExposureEvent(exposure: exposure),
                    );
                  },
                  onGridToggle: () {
                    setState(() {
                      _gridEnabled = !_gridEnabled;
                    });
                  },
                  onAutoFocusToggle: () {
                    setState(() {
                      _autoFocusEnabled = !_autoFocusEnabled;
                    });
                  },
                  onClose: () {
                    setState(() {
                      _showSettings = false;
                    });
                  },
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCameraPreview(CameraState state) {
    if (state is CameraLoading) {
      return _buildLoadingView();
    }

    if (state is CameraReady || state is CameraCapturing) {
      // Get camera controller from repository
      final repository = sl<CameraRepositoryImpl>();
      final cameraController = repository.cameraController;

      return GestureDetector(
        onScaleUpdate: (details) {
          // Handle pinch-to-zoom
          if (details.scale != 1.0) {
            final newZoom = (_currentZoom * details.scale).clamp(1.0, 10.0);
            if ((newZoom - _currentZoom).abs() > 0.1) {
              setState(() {
                _currentZoom = newZoom;
              });
              HapticFeedback.selectionClick();
              // Apply zoom to camera
              context.read<CameraBloc>().add(SetZoomEvent(zoom: newZoom));
            }
          }
        },
        child: Container(
          color: Colors.black,
          child: Stack(
            children: [
              // Actual camera preview
              CameraPreviewWidget(controller: cameraController),

              // Zoom indicator
              if (_currentZoom > 1.0)
                Positioned(
                  top: MediaQuery.of(context).padding.top + 120,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        '${_currentZoom.toStringAsFixed(1)}x',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      );
    }

    return _buildLoadingView();
  }

  Widget _buildLoadingView() {
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white, strokeWidth: 2),
            SizedBox(height: 16),
            Text(
              'Initializing Camera...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
