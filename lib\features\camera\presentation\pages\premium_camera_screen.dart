import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:camera/camera.dart';
import '../../../../core/theme/app_theme.dart';
import '../widgets/premium_camera_controls.dart';
import '../widgets/premium_camera_overlay.dart';
import '../widgets/premium_camera_settings.dart';

class PremiumCameraScreen extends StatefulWidget {
  const PremiumCameraScreen({super.key});

  @override
  State<PremiumCameraScreen> createState() => _PremiumCameraScreenState();
}

class _PremiumCameraScreenState extends State<PremiumCameraScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  CameraController? _cameraController;
  late AnimationController _overlayController;
  late AnimationController _controlsController;
  late Animation<double> _overlayAnimation;
  late Animation<double> _controlsAnimation;

  bool _isInitialized = false;
  bool _isCapturing = false;
  bool _showSettings = false;
  bool _flashEnabled = false;
  bool _gridEnabled = true;
  bool _autoFocusEnabled = true;
  bool _isAutoCapturing = false;
  final double _currentZoom = 1.0;
  final double _currentExposure = 0.0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeAnimations();
    _initializeCamera();
    _setSystemUIOverlay();
  }

  void _initializeAnimations() {
    _overlayController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _controlsController = AnimationController(
      duration: AppTheme.animationSlow,
      vsync: this,
    );

    _overlayAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _overlayController,
        curve: AppTheme.curveEmphasized,
      ),
    );

    _controlsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controlsController,
        curve: AppTheme.curveEmphasized,
      ),
    );
  }

  void _setSystemUIOverlay() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
  }

  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isNotEmpty) {
        _cameraController = CameraController(
          cameras.first,
          ResolutionPreset.high,
          enableAudio: false,
        );

        await _cameraController!.initialize();

        if (mounted) {
          setState(() {
            _isInitialized = true;
          });
          _startEntryAnimations();
        }
      }
    } catch (e) {
      debugPrint('Camera initialization error: $e');
    }
  }

  void _startEntryAnimations() {
    _overlayController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _controlsController.forward();
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _overlayController.dispose();
    _controlsController.dispose();
    _cameraController?.dispose();
    _resetSystemUIOverlay();
    super.dispose();
  }

  void _resetSystemUIOverlay() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      _cameraController?.dispose();
    } else if (state == AppLifecycleState.resumed) {
      _initializeCamera();
    }
  }

  Future<void> _captureImage() async {
    if (!_isInitialized || _isCapturing) return;

    setState(() {
      _isCapturing = true;
    });

    try {
      HapticFeedback.mediumImpact();

      final image = await _cameraController!.takePicture();

      // Navigate to document processing screen
      if (mounted) {
        Navigator.of(
          context,
        ).pushNamed('/document-processing', arguments: image.path);
      }
    } catch (e) {
      debugPrint('Capture error: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isCapturing = false;
        });
      }
    }
  }

  void _toggleFlash() {
    setState(() {
      _flashEnabled = !_flashEnabled;
    });

    _cameraController?.setFlashMode(
      _flashEnabled ? FlashMode.torch : FlashMode.off,
    );

    HapticFeedback.lightImpact();
  }

  void _toggleSettings() {
    setState(() {
      _showSettings = !_showSettings;
    });
    HapticFeedback.lightImpact();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryDarkColor,
      body: Stack(
        children: [
          // Camera Preview
          if (_isInitialized)
            Positioned.fill(child: CameraPreview(_cameraController!))
          else
            _buildLoadingView(),

          // Camera Overlay
          AnimatedBuilder(
            animation: _overlayAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _overlayAnimation.value,
                child: PremiumCameraOverlay(
                  isAutoCapturing: _isAutoCapturing,
                  onAutoCapture: () {
                    setState(() {
                      _isAutoCapturing = !_isAutoCapturing;
                    });
                    HapticFeedback.lightImpact();
                  },
                ),
              );
            },
          ),

          // Camera Controls
          AnimatedBuilder(
            animation: _controlsAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, (1 - _controlsAnimation.value) * 100),
                child: Opacity(
                  opacity: _controlsAnimation.value,
                  child: PremiumCameraControls(
                    onCapture: _captureImage,
                    onSwitchCamera: () {
                      // TODO: Implement camera switching
                      HapticFeedback.lightImpact();
                    },
                    onToggleFlash: _toggleFlash,
                    onOpenGallery: () {
                      // TODO: Implement gallery opening
                      HapticFeedback.lightImpact();
                    },
                    onShowSettings: _toggleSettings,
                    isCapturing: _isCapturing,
                    isFlashOn: _flashEnabled,
                  ),
                ),
              );
            },
          ),

          // Settings Panel
          if (_showSettings)
            PremiumCameraSettings(
              zoom: _currentZoom,
              exposure: _currentExposure,
              gridEnabled: _gridEnabled,
              autoFocusEnabled: _autoFocusEnabled,
              onZoomChanged: (zoom) {
                setState(() {
                  _currentZoom = zoom;
                });
                // TODO: Apply zoom to camera
              },
              onExposureChanged: (exposure) {
                setState(() {
                  _currentExposure = exposure;
                });
                // TODO: Apply exposure to camera
              },
              onGridToggle: () {
                setState(() {
                  _gridEnabled = !_gridEnabled;
                });
              },
              onAutoFocusToggle: () {
                setState(() {
                  _autoFocusEnabled = !_autoFocusEnabled;
                });
              },
              onClose: () {
                setState(() {
                  _showSettings = false;
                });
              },
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingView() {
    return Container(
      color: AppTheme.primaryDarkColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: AppTheme.spacing80,
              height: AppTheme.spacing80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: AppTheme.primaryGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(AppTheme.radius2XL),
              ),
              child: Center(
                child: SizedBox(
                  width: AppTheme.spacing32,
                  height: AppTheme.spacing32,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.textOnPrimaryColor,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: AppTheme.spacing24),
            Text(
              'Initializing Camera...',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppTheme.textOnPrimaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
