import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/di/injection_container.dart';
import '../bloc/camera_bloc.dart';
import '../bloc/camera_event.dart';
import '../bloc/camera_state.dart';
import '../widgets/premium_camera_controls.dart';
import '../widgets/premium_camera_overlay.dart';
import '../widgets/premium_camera_settings.dart';

class PremiumCameraScreen extends StatelessWidget {
  const PremiumCameraScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<CameraBloc>()..add(InitializeCameraEvent()),
      child: const PremiumCameraView(),
    );
  }
}

class PremiumCameraView extends StatefulWidget {
  const PremiumCameraView({super.key});

  @override
  State<PremiumCameraView> createState() => _PremiumCameraViewState();
}

class _PremiumCameraViewState extends State<PremiumCameraView>
    with TickerProviderStateMixin {
  late AnimationController _controlsController;
  late Animation<double> _controlsAnimation;

  bool _showSettings = false;
  bool _gridEnabled = true;
  bool _autoFocusEnabled = true;
  bool _isAutoCapturing = false;
  double _currentZoom = 1.0;
  double _currentExposure = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setSystemUIOverlay();
  }

  void _initializeAnimations() {
    _controlsController = AnimationController(
      duration: AppTheme.animationSlow,
      vsync: this,
    );

    _controlsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controlsController,
        curve: AppTheme.curveEmphasized,
      ),
    );

    // Start controls animation
    _controlsController.forward();
  }

  void _setSystemUIOverlay() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
  }

  @override
  void dispose() {
    _controlsController.dispose();
    _resetSystemUIOverlay();
    super.dispose();
  }

  void _resetSystemUIOverlay() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  void _captureImage() {
    HapticFeedback.mediumImpact();
    context.read<CameraBloc>().add(CaptureImageEvent());
  }

  void _toggleFlash() {
    HapticFeedback.lightImpact();
    context.read<CameraBloc>().add(ToggleFlashEvent());
  }

  void _toggleSettings() {
    setState(() {
      _showSettings = !_showSettings;
    });
    HapticFeedback.lightImpact();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryDarkColor,
      body: BlocConsumer<CameraBloc, CameraState>(
        listener: (context, state) {
          if (state is CameraError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppTheme.errorColor,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                ),
              ),
            );
          }

          if (state is CameraImageCaptured) {
            // Navigate to document processing screen
            Navigator.of(
              context,
            ).pushNamed('/document-processing', arguments: state.imageBytes);
          }
        },
        builder: (context, state) {
          return Stack(
            children: [
              // Camera Preview
              _buildCameraPreview(state),

              // Camera Overlay
              PremiumCameraOverlay(
                isAutoCapturing: _isAutoCapturing,
                onAutoCapture: () {
                  setState(() {
                    _isAutoCapturing = !_isAutoCapturing;
                  });
                  HapticFeedback.lightImpact();
                },
              ),

              // Camera Controls
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: AnimatedBuilder(
                  animation: _controlsAnimation,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(0, (1 - _controlsAnimation.value) * 100),
                      child: Opacity(
                        opacity: _controlsAnimation.value,
                        child: PremiumCameraControls(
                          onCapture: _captureImage,
                          onSwitchCamera: () {
                            // TODO: Implement camera switching
                            HapticFeedback.lightImpact();
                          },
                          onToggleFlash: _toggleFlash,
                          onOpenGallery: () {
                            // TODO: Implement gallery opening
                            HapticFeedback.lightImpact();
                          },
                          onShowSettings: _toggleSettings,
                          isCapturing: state is CameraCapturing,
                          isFlashOn:
                              state is CameraReady ? state.isFlashOn : false,
                        ),
                      ),
                    );
                  },
                ),
              ),

              // Settings Panel
              if (_showSettings)
                PremiumCameraSettings(
                  zoom: _currentZoom,
                  exposure: _currentExposure,
                  gridEnabled: _gridEnabled,
                  autoFocusEnabled: _autoFocusEnabled,
                  onZoomChanged: (zoom) {
                    setState(() {
                      _currentZoom = zoom;
                    });
                    // TODO: Apply zoom to camera
                  },
                  onExposureChanged: (exposure) {
                    setState(() {
                      _currentExposure = exposure;
                    });
                    // TODO: Apply exposure to camera
                  },
                  onGridToggle: () {
                    setState(() {
                      _gridEnabled = !_gridEnabled;
                    });
                  },
                  onAutoFocusToggle: () {
                    setState(() {
                      _autoFocusEnabled = !_autoFocusEnabled;
                    });
                  },
                  onClose: () {
                    setState(() {
                      _showSettings = false;
                    });
                  },
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCameraPreview(CameraState state) {
    if (state is CameraLoading) {
      return _buildLoadingView();
    }

    if (state is CameraReady || state is CameraCapturing) {
      return Container(
        color: AppTheme.primaryDarkColor,
        child: const Center(
          child: Text(
            'Camera Preview\n(CamerAwesome Integration)',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      );
    }

    return _buildLoadingView();
  }

  Widget _buildLoadingView() {
    return Container(
      color: AppTheme.primaryDarkColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: AppTheme.spacing80,
              height: AppTheme.spacing80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: AppTheme.primaryGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(AppTheme.radius2XL),
              ),
              child: Center(
                child: SizedBox(
                  width: AppTheme.spacing32,
                  height: AppTheme.spacing32,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.textOnPrimaryColor,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: AppTheme.spacing24),
            Text(
              'Initializing Camera...',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppTheme.textOnPrimaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
