import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import '../../domain/repositories/camera_repository.dart';

abstract class CameraEvent extends Equatable {
  const CameraEvent();

  @override
  List<Object> get props => [];
}

class InitializeCameraEvent extends CameraEvent {}

class CaptureImageEvent extends <PERSON>Event {}

class DisposeCameraEvent extends <PERSON>Event {}

class ToggleFlashEvent extends CameraEvent {}

class SwitchCameraEvent extends CameraEvent {}

class SetZoomEvent extends CameraEvent {
  final double zoom;

  const SetZoomEvent({required this.zoom});

  @override
  List<Object> get props => [zoom];
}

class SetExposureEvent extends CameraEvent {
  final double exposure;

  const SetExposureEvent({required this.exposure});

  @override
  List<Object> get props => [exposure];
}

class SetFocusEvent extends CameraEvent {
  final Offset point;

  const SetFocusEvent({required this.point});

  @override
  List<Object> get props => [point];
}

class SetFlashModeEvent extends Camera<PERSON>vent {
  final CameraFlashMode flashMode;

  const SetFlashModeEvent({required this.flashMode});

  @override
  List<Object> get props => [flashMode];
}
