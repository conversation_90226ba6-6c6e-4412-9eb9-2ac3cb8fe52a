import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Native-style camera settings panel
class NativeCameraSettings extends StatefulWidget {
  final double zoom;
  final double exposure;
  final bool gridEnabled;
  final bool autoFocusEnabled;
  final Function(double) onZoomChanged;
  final Function(double) onExposureChanged;
  final VoidCallback onGridToggle;
  final VoidCallback onAutoFocusToggle;
  final VoidCallback onClose;

  const NativeCameraSettings({
    super.key,
    required this.zoom,
    required this.exposure,
    required this.gridEnabled,
    required this.autoFocusEnabled,
    required this.onZoomChanged,
    required this.onExposureChanged,
    required this.onGridToggle,
    required this.onAutoFocusToggle,
    required this.onClose,
  });

  @override
  State<NativeCameraSettings> createState() => _NativeCameraSettingsState();
}

class _NativeCameraSettingsState extends State<NativeCameraSettings> {
  @override
  Widget build(BuildContext context) {
    return Positioned(
      right: 16,
      top: MediaQuery.of(context).padding.top + 80,
      bottom: 200,
      child: Container(
        width: 60,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(30),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Close button
            _buildSettingsButton(icon: Icons.close, onPressed: widget.onClose),

            // Zoom control
            _buildVerticalSlider(
              value: widget.zoom,
              min: 1.0,
              max: 10.0,
              onChanged: widget.onZoomChanged,
              icon: Icons.zoom_in,
            ),

            // Exposure control
            _buildVerticalSlider(
              value: widget.exposure,
              min: -2.0,
              max: 2.0,
              onChanged: widget.onExposureChanged,
              icon: Icons.exposure,
            ),

            // Grid toggle
            _buildSettingsButton(
              icon: widget.gridEnabled ? Icons.grid_on : Icons.grid_off,
              onPressed: widget.onGridToggle,
              isActive: widget.gridEnabled,
            ),

            // Auto focus toggle
            _buildSettingsButton(
              icon:
                  widget.autoFocusEnabled
                      ? Icons.center_focus_strong
                      : Icons.center_focus_weak,
              onPressed: widget.onAutoFocusToggle,
              isActive: widget.autoFocusEnabled,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsButton({
    required IconData icon,
    required VoidCallback onPressed,
    bool isActive = false,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onPressed();
      },
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color:
              isActive
                  ? Colors.white.withValues(alpha: 0.2)
                  : Colors.transparent,
          shape: BoxShape.circle,
          border:
              isActive
                  ? Border.all(
                    color: Colors.white.withValues(alpha: 0.5),
                    width: 1,
                  )
                  : null,
        ),
        child: Icon(icon, color: Colors.white, size: 20),
      ),
    );
  }

  Widget _buildVerticalSlider({
    required double value,
    required double min,
    required double max,
    required Function(double) onChanged,
    required IconData icon,
  }) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 18),
        const SizedBox(height: 8),
        SizedBox(
          height: 100,
          child: RotatedBox(
            quarterTurns: 3,
            child: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Colors.white,
                inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
                thumbColor: Colors.white,
                overlayColor: Colors.white.withValues(alpha: 0.2),
                trackHeight: 2,
                thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
              ),
              child: Slider(
                value: value,
                min: min,
                max: max,
                onChanged: onChanged,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          _formatValue(value, min, max),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  String _formatValue(double value, double min, double max) {
    if (min == 1.0 && max == 10.0) {
      // Zoom value
      return '${value.toStringAsFixed(1)}x';
    } else {
      // Exposure value
      return value.toStringAsFixed(1);
    }
  }
}
