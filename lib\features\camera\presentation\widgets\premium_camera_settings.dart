import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumCameraSettings extends StatefulWidget {
  final double zoom;
  final double exposure;
  final bool gridEnabled;
  final bool autoFocusEnabled;
  final Function(double) onZoomChanged;
  final Function(double) onExposureChanged;
  final VoidCallback onGridToggle;
  final VoidCallback onAutoFocusToggle;
  final VoidCallback onClose;

  const PremiumCameraSettings({
    super.key,
    required this.zoom,
    required this.exposure,
    required this.gridEnabled,
    required this.autoFocusEnabled,
    required this.onZoomChanged,
    required this.onExposureChanged,
    required this.onGridToggle,
    required this.onAutoFocusToggle,
    required this.onClose,
  });

  @override
  State<PremiumCameraSettings> createState() => _PremiumCameraSettingsState();
}

class _PremiumCameraSettingsState extends State<PremiumCameraSettings>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startEntryAnimation();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _slideController,
        curve: AppTheme.curveEmphasized,
      ),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: AppTheme.curveStandard),
    );
  }

  void _startEntryAnimation() {
    _fadeController.forward();
    _slideController.forward();
  }

  void _closeSettings() async {
    await _slideController.reverse();
    await _fadeController.reverse();
    widget.onClose();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_slideAnimation, _fadeAnimation]),
      builder: (context, child) {
        return Stack(
          children: [
            // Backdrop
            GestureDetector(
              onTap: _closeSettings,
              child: Container(
                width: double.infinity,
                height: double.infinity,
                color: AppTheme.primaryDarkColor.withValues(
                  alpha: 0.7 * _fadeAnimation.value,
                ),
              ),
            ),

            // Settings Panel
            Positioned(
              right: 0,
              top: 0,
              bottom: 0,
              child: SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildSettingsPanel(),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSettingsPanel() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.8,
      decoration: BoxDecoration(
        color: AppTheme.glassColor,
        border: Border.all(color: AppTheme.glassBorderColor, width: 1),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryDarkColor.withValues(alpha: 0.5),
            blurRadius: AppTheme.shadowBlur2XL,
            offset: const Offset(-AppTheme.spacing8, 0),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(),

            // Settings Content
            Expanded(child: _buildSettingsContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing24),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppTheme.glassBorderColor, width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Camera Settings',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textOnPrimaryColor,
              fontWeight: FontWeight.w700,
            ),
          ),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              _closeSettings();
            },
            child: Container(
              padding: const EdgeInsets.all(AppTheme.spacing8),
              decoration: BoxDecoration(
                color: AppTheme.glassColor,
                borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                border: Border.all(color: AppTheme.glassBorderColor, width: 1),
              ),
              child: Icon(
                Icons.close_rounded,
                color: AppTheme.textOnPrimaryColor,
                size: AppTheme.spacing20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsContent() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacing24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Zoom Control
          _buildSliderSetting(
            title: 'Zoom',
            value: widget.zoom,
            min: 1.0,
            max: 10.0,
            divisions: 90,
            onChanged: widget.onZoomChanged,
            formatValue: (value) => '${value.toStringAsFixed(1)}x',
          ),

          const SizedBox(height: AppTheme.spacing24),

          // Exposure Control
          _buildSliderSetting(
            title: 'Exposure',
            value: widget.exposure,
            min: -2.0,
            max: 2.0,
            divisions: 40,
            onChanged: widget.onExposureChanged,
            formatValue: (value) => value.toStringAsFixed(1),
          ),

          const SizedBox(height: AppTheme.spacing32),

          _buildSettingsSection(
            title: 'Display',
            children: [
              _buildSettingsTile(
                icon: Icons.grid_3x3_outlined,
                title: 'Grid Lines',
                subtitle: 'Show composition grid',
                value: widget.gridEnabled,
                onChanged: (value) {
                  HapticFeedback.lightImpact();
                  widget.onGridToggle();
                },
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacing32),

          _buildSettingsSection(
            title: 'Camera',
            children: [
              _buildSettingsTile(
                icon: Icons.center_focus_strong_outlined,
                title: 'Auto Focus',
                subtitle: 'Automatic focus adjustment',
                value: widget.autoFocusEnabled,
                onChanged: (value) {
                  HapticFeedback.lightImpact();
                  widget.onAutoFocusToggle();
                },
              ),
              _buildSettingsTile(
                icon: Icons.hdr_auto_outlined,
                title: 'Auto Enhancement',
                subtitle: 'Enhance image quality automatically',
                value: true,
                onChanged: (value) {
                  HapticFeedback.lightImpact();
                  // Handle auto enhancement toggle
                },
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacing32),

          _buildSettingsSection(
            title: 'Quality',
            children: [_buildQualitySelector()],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title.toUpperCase(),
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
            color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.7),
            fontWeight: FontWeight.w600,
            letterSpacing: 1.2,
          ),
        ),
        const SizedBox(height: AppTheme.spacing16),
        ...children,
      ],
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacing12),
      padding: const EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: AppTheme.glassColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusLG),
        border: Border.all(color: AppTheme.glassBorderColor, width: 1),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacing10),
            decoration: BoxDecoration(
              color: AppTheme.brandPrimary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusMD),
            ),
            child: Icon(
              icon,
              color: AppTheme.brandPrimary,
              size: AppTheme.spacing20,
            ),
          ),

          const SizedBox(width: AppTheme.spacing16),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: AppTheme.textOnPrimaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppTheme.spacing2),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),

          Switch.adaptive(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.brandPrimary,
            activeTrackColor: AppTheme.brandPrimary.withValues(alpha: 0.3),
            inactiveThumbColor: AppTheme.textOnPrimaryColor.withValues(
              alpha: 0.5,
            ),
            inactiveTrackColor: AppTheme.textOnPrimaryColor.withValues(
              alpha: 0.2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQualitySelector() {
    final qualities = ['High', 'Medium', 'Low'];
    final selectedQuality = 'High';

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: AppTheme.glassColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusLG),
        border: Border.all(color: AppTheme.glassBorderColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacing10),
                decoration: BoxDecoration(
                  color: AppTheme.brandPrimary.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMD),
                ),
                child: Icon(
                  Icons.high_quality_outlined,
                  color: AppTheme.brandPrimary,
                  size: AppTheme.spacing20,
                ),
              ),
              const SizedBox(width: AppTheme.spacing16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Image Quality',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: AppTheme.textOnPrimaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacing2),
                    Text(
                      'Higher quality uses more storage',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textOnPrimaryColor.withValues(
                          alpha: 0.7,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacing16),

          Row(
            children:
                qualities.map((quality) {
                  final isSelected = quality == selectedQuality;
                  return Expanded(
                    child: GestureDetector(
                      onTap: () {
                        HapticFeedback.lightImpact();
                        // Handle quality selection
                      },
                      child: Container(
                        margin: EdgeInsets.only(
                          right:
                              quality != qualities.last ? AppTheme.spacing8 : 0,
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: AppTheme.spacing12,
                        ),
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? AppTheme.brandPrimary.withValues(alpha: 0.3)
                                  : AppTheme.glassColor,
                          borderRadius: BorderRadius.circular(
                            AppTheme.radiusMD,
                          ),
                          border: Border.all(
                            color:
                                isSelected
                                    ? AppTheme.brandPrimary
                                    : AppTheme.glassBorderColor,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          quality,
                          textAlign: TextAlign.center,
                          style: Theme.of(
                            context,
                          ).textTheme.bodySmall?.copyWith(
                            color:
                                isSelected
                                    ? AppTheme.brandPrimary
                                    : AppTheme.textOnPrimaryColor.withValues(
                                      alpha: 0.7,
                                    ),
                            fontWeight:
                                isSelected ? FontWeight.w600 : FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }
}
