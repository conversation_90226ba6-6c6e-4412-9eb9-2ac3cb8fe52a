import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import '../../domain/repositories/camera_repository.dart';

class CameraRepositoryImpl implements CameraRepository {
  final StreamController<CameraAwesomeState> _stateController =
      StreamController<CameraAwesomeState>.broadcast();
  final StreamController<bool> _documentDetectionController =
      StreamController<bool>.broadcast();

  CameraController? _cameraController;
  List<CameraDescription> _cameras = [];
  int _currentCameraIndex = 0;

  // Camera state
  CameraFlashMode _currentFlashMode = CameraFlashMode.off;
  CameraSensor _currentSensor = CameraSensor.back;
  double _currentZoom = 1.0;
  double _currentExposure = 0.0;
  bool _isAutoCapturing = false;

  // Getter for camera controller
  CameraController? get cameraController => _cameraController;

  @override
  Future<bool> initializeCamera() async {
    try {
      _stateController.add(CameraAwesomeState.initializing);

      // Get available cameras
      _cameras = await availableCameras();

      if (_cameras.isEmpty) {
        _stateController.add(CameraAwesomeState.error);
        return false;
      }

      // Initialize camera controller with back camera
      _currentCameraIndex = _cameras.indexWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
      );

      if (_currentCameraIndex == -1) {
        _currentCameraIndex = 0; // Fallback to first available camera
      }

      _cameraController = CameraController(
        _cameras[_currentCameraIndex],
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _cameraController!.initialize();

      _stateController.add(CameraAwesomeState.ready);
      return true;
    } catch (e) {
      _stateController.add(CameraAwesomeState.error);
      return false;
    }
  }

  @override
  Future<Uint8List> captureImage() async {
    try {
      if (_cameraController == null ||
          !_cameraController!.value.isInitialized) {
        throw Exception('Camera not initialized');
      }

      _stateController.add(CameraAwesomeState.capturing);

      // Capture image
      final XFile imageFile = await _cameraController!.takePicture();
      final Uint8List imageBytes = await imageFile.readAsBytes();

      _stateController.add(CameraAwesomeState.ready);
      return imageBytes;
    } catch (e) {
      _stateController.add(CameraAwesomeState.error);
      rethrow;
    }
  }

  @override
  Future<void> disposeCamera() async {
    await _cameraController?.dispose();
    _cameraController = null;
    await _stateController.close();
    await _documentDetectionController.close();
  }

  @override
  Future<void> setFlashMode(CameraFlashMode flashMode) async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    _currentFlashMode = flashMode;

    FlashMode cameraFlashMode;
    switch (flashMode) {
      case CameraFlashMode.off:
        cameraFlashMode = FlashMode.off;
        break;
      case CameraFlashMode.on:
        cameraFlashMode = FlashMode.always;
        break;
      case CameraFlashMode.auto:
        cameraFlashMode = FlashMode.auto;
        break;
      case CameraFlashMode.torch:
        cameraFlashMode = FlashMode.torch;
        break;
    }

    await _cameraController!.setFlashMode(cameraFlashMode);
  }

  @override
  Future<void> switchCamera() async {
    if (_cameras.length < 2) return;

    // Dispose current controller
    await _cameraController?.dispose();

    // Switch to the other camera
    _currentSensor =
        _currentSensor == CameraSensor.back
            ? CameraSensor.front
            : CameraSensor.back;

    // Find the camera with the desired lens direction
    final targetLensDirection =
        _currentSensor == CameraSensor.back
            ? CameraLensDirection.back
            : CameraLensDirection.front;

    _currentCameraIndex = _cameras.indexWhere(
      (camera) => camera.lensDirection == targetLensDirection,
    );

    if (_currentCameraIndex == -1) return;

    // Initialize new camera controller
    _cameraController = CameraController(
      _cameras[_currentCameraIndex],
      ResolutionPreset.high,
      enableAudio: false,
    );

    await _cameraController!.initialize();
  }

  @override
  Future<void> setZoom(double zoom) async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    _currentZoom = zoom.clamp(1.0, 10.0);
    await _cameraController!.setZoomLevel(_currentZoom);
  }

  @override
  Future<void> setExposure(double exposure) async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    _currentExposure = exposure.clamp(-2.0, 2.0);
    await _cameraController!.setExposureOffset(_currentExposure);
  }

  @override
  Future<void> setFocus(Offset point) async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    await _cameraController!.setFocusPoint(point);
  }

  @override
  Future<void> startAutoCapture() async {
    _isAutoCapturing = true;
    // Implementation will be added with document detection
  }

  @override
  Future<void> stopAutoCapture() async {
    _isAutoCapturing = false;
    // Implementation will be added with document detection
  }

  @override
  Stream<CameraAwesomeState> get cameraStateStream => _stateController.stream;

  @override
  Stream<bool> get documentDetectionStream =>
      _documentDetectionController.stream;
}
