import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'document_mode_selector.dart';

/// Native document detection overlay with Adobe Scan-style features
class NativeDocumentOverlay extends StatefulWidget {
  final bool isAutoCapturing;
  final bool showGrid;
  final DocumentMode selectedMode;
  final VoidCallback onAutoCapture;
  final Function(DocumentMode) onModeChanged;

  const NativeDocumentOverlay({
    super.key,
    required this.isAutoCapturing,
    required this.showGrid,
    required this.selectedMode,
    required this.onAutoCapture,
    required this.onModeChanged,
  });

  @override
  State<NativeDocumentOverlay> createState() => _NativeDocumentOverlayState();
}

class _NativeDocumentOverlayState extends State<NativeDocumentOverlay>
    with TickerProviderStateMixin {
  late AnimationController _cornerController;
  late AnimationController _gridController;
  late Animation<double> _cornerAnimation;
  late Animation<double> _gridAnimation;

  Offset? _focusPoint;
  bool _showFocusIndicator = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _cornerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _gridController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _cornerAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _cornerController, curve: Curves.easeInOut),
    );

    _gridAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _gridController, curve: Curves.easeInOut),
    );

    // Start corner animation
    _cornerController.repeat(reverse: true);

    if (widget.showGrid) {
      _gridController.forward();
    }
  }

  @override
  void didUpdateWidget(NativeDocumentOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.showGrid != oldWidget.showGrid) {
      if (widget.showGrid) {
        _gridController.forward();
      } else {
        _gridController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _cornerController.dispose();
    _gridController.dispose();
    super.dispose();
  }

  void _handleTap(TapDownDetails details) {
    setState(() {
      _focusPoint = details.localPosition;
      _showFocusIndicator = true;
    });

    HapticFeedback.lightImpact();

    // Hide focus indicator after 2 seconds
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _showFocusIndicator = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: GestureDetector(
        onTapDown: _handleTap,
        child: Stack(
          children: [
            // Document Frame
            _buildDocumentFrame(),

            // Grid Overlay
            if (widget.showGrid)
              AnimatedBuilder(
                animation: _gridAnimation,
                builder: (context, child) {
                  return Opacity(
                    opacity: _gridAnimation.value * 0.4,
                    child: _buildGridOverlay(),
                  );
                },
              ),

            // Focus Indicator
            if (_showFocusIndicator && _focusPoint != null)
              _buildFocusIndicator(),

            // Document Mode Selector
            Positioned(
              left: 0,
              right: 0,
              bottom: 120,
              child: DocumentModeSelector(
                selectedMode: widget.selectedMode,
                onModeChanged: widget.onModeChanged,
              ),
            ),

            // Auto-capture indicator
            if (widget.isAutoCapturing) _buildAutoCaptureBadge(),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentFrame() {
    return AnimatedBuilder(
      animation: _cornerAnimation,
      builder: (context, child) {
        return CustomPaint(
          painter: DocumentFramePainter(
            cornerOpacity: _cornerAnimation.value,
            isDetected: widget.isAutoCapturing,
          ),
          size: Size.infinite,
        );
      },
    );
  }

  Widget _buildGridOverlay() {
    return CustomPaint(painter: GridPainter(), size: Size.infinite);
  }

  Widget _buildFocusIndicator() {
    return Positioned(
      left: _focusPoint!.dx - 30,
      top: _focusPoint!.dy - 30,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.yellow, width: 2),
          borderRadius: BorderRadius.circular(4),
        ),
      ),
    );
  }

  Widget _buildAutoCaptureBadge() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 80,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.green,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.auto_awesome, color: Colors.white, size: 16),
              const SizedBox(width: 8),
              const Text(
                'Auto-capture ON',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Custom painter for document frame with corner indicators
class DocumentFramePainter extends CustomPainter {
  final double cornerOpacity;
  final bool isDetected;

  DocumentFramePainter({required this.cornerOpacity, required this.isDetected});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = (isDetected ? Colors.green : Colors.white).withValues(
            alpha: cornerOpacity,
          )
          ..strokeWidth = 3
          ..style = PaintingStyle.stroke;

    final rect = Rect.fromLTWH(
      size.width * 0.1,
      size.height * 0.2,
      size.width * 0.8,
      size.height * 0.6,
    );

    // Draw corner indicators
    const cornerLength = 30.0;

    // Top-left corner
    canvas.drawLine(
      Offset(rect.left, rect.top + cornerLength),
      Offset(rect.left, rect.top),
      paint,
    );
    canvas.drawLine(
      Offset(rect.left, rect.top),
      Offset(rect.left + cornerLength, rect.top),
      paint,
    );

    // Top-right corner
    canvas.drawLine(
      Offset(rect.right - cornerLength, rect.top),
      Offset(rect.right, rect.top),
      paint,
    );
    canvas.drawLine(
      Offset(rect.right, rect.top),
      Offset(rect.right, rect.top + cornerLength),
      paint,
    );

    // Bottom-left corner
    canvas.drawLine(
      Offset(rect.left, rect.bottom - cornerLength),
      Offset(rect.left, rect.bottom),
      paint,
    );
    canvas.drawLine(
      Offset(rect.left, rect.bottom),
      Offset(rect.left + cornerLength, rect.bottom),
      paint,
    );

    // Bottom-right corner
    canvas.drawLine(
      Offset(rect.right - cornerLength, rect.bottom),
      Offset(rect.right, rect.bottom),
      paint,
    );
    canvas.drawLine(
      Offset(rect.right, rect.bottom),
      Offset(rect.right, rect.bottom - cornerLength),
      paint,
    );
  }

  @override
  bool shouldRepaint(DocumentFramePainter oldDelegate) {
    return oldDelegate.cornerOpacity != cornerOpacity ||
        oldDelegate.isDetected != isDetected;
  }
}

/// Custom painter for grid overlay
class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.3)
          ..strokeWidth = 1;

    // Draw vertical lines
    for (int i = 1; i < 3; i++) {
      final x = size.width * i / 3;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Draw horizontal lines
    for (int i = 1; i < 3; i++) {
      final y = size.height * i / 3;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(GridPainter oldDelegate) => false;
}
