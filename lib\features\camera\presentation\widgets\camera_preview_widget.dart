import 'package:flutter/material.dart';
import 'package:camera/camera.dart';

/// Widget that displays the actual camera preview in full-screen mode
///
/// This widget automatically scales the camera preview to fill the entire screen
/// while maintaining the correct aspect ratio. It handles different screen sizes
/// and orientations by calculating the optimal scaling approach.
///
/// Features:
/// - Full-screen coverage like native camera apps
/// - Maintains camera aspect ratio without distortion
/// - <PERSON>les both portrait and landscape orientations
/// - Clips overflow to prevent UI layout issues
/// - Centers the camera preview for optimal viewing
class CameraPreviewWidget extends StatelessWidget {
  final CameraController? controller;

  const CameraPreviewWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    if (controller == null || !controller!.value.isInitialized) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2),
        ),
      );
    }

    return _buildFullScreenCameraPreview(context);
  }

  Widget _buildFullScreenCameraPreview(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final cameraRatio = controller!.value.aspectRatio;
    final screenRatio = screenSize.width / screenSize.height;

    // Determine if we need to scale to fill width or height
    Widget cameraWidget;

    if (cameraRatio > screenRatio) {
      // Camera is wider than screen - scale to fill height
      cameraWidget = SizedBox(
        width: screenSize.height * cameraRatio,
        height: screenSize.height,
        child: CameraPreview(controller!),
      );
    } else {
      // Camera is taller than screen - scale to fill width
      cameraWidget = SizedBox(
        width: screenSize.width,
        height: screenSize.width / cameraRatio,
        child: CameraPreview(controller!),
      );
    }

    return SizedBox(
      width: screenSize.width,
      height: screenSize.height,
      child: ClipRect(
        child: OverflowBox(alignment: Alignment.center, child: cameraWidget),
      ),
    );
  }
}
