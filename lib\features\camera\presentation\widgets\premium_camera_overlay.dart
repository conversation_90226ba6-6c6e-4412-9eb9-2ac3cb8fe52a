import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumCameraOverlay extends StatefulWidget {
  final bool isAutoCapturing;
  final VoidCallback onAutoCapture;

  const PremiumCameraOverlay({
    super.key,
    required this.isAutoCapturing,
    required this.onAutoCapture,
  });

  @override
  State<PremiumCameraOverlay> createState() => _PremiumCameraOverlayState();
}

class _PremiumCameraOverlayState extends State<PremiumCameraOverlay>
    with TickerProviderStateMixin {
  late AnimationController _gridController;
  late AnimationController _focusController;
  late Animation<double> _gridAnimation;
  late Animation<double> _focusAnimation;

  Offset? _focusPoint;
  bool _showFocusIndicator = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _gridController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _focusController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _gridAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _gridController, curve: AppTheme.curveStandard),
    );

    _focusAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _focusController,
        curve: AppTheme.curveEmphasized,
      ),
    );

    if (widget.isAutoCapturing) {
      _gridController.forward();
    }
  }

  @override
  void didUpdateWidget(PremiumCameraOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isAutoCapturing != oldWidget.isAutoCapturing) {
      if (widget.isAutoCapturing) {
        _gridController.forward();
      } else {
        _gridController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _gridController.dispose();
    _focusController.dispose();
    super.dispose();
  }

  void _handleTap(TapDownDetails details) {
    setState(() {
      _focusPoint = details.localPosition;
      _showFocusIndicator = true;
    });

    _focusController.reset();
    _focusController.forward().then((_) {
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          setState(() {
            _showFocusIndicator = false;
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: GestureDetector(
        onTapDown: _handleTap,
        child: Stack(
          children: [
            // Document Frame Overlay
            _buildDocumentFrame(),

            // Grid Overlay
            if (widget.isAutoCapturing)
              AnimatedBuilder(
                animation: _gridAnimation,
                builder: (context, child) {
                  return Opacity(
                    opacity: _gridAnimation.value * 0.3,
                    child: _buildGridOverlay(),
                  );
                },
              ),

            // Focus Indicator
            if (_showFocusIndicator && _focusPoint != null)
              AnimatedBuilder(
                animation: _focusAnimation,
                builder: (context, child) {
                  return _buildFocusIndicator();
                },
              ),

            // Corner Guidelines
            _buildCornerGuidelines(),

            // Top Info Bar
            _buildTopInfoBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentFrame() {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacing32),
      decoration: BoxDecoration(
        border: Border.all(
          color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.3),
          width: 2,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusLG),
      ),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: AppTheme.brandPrimary.withValues(alpha: 0.5),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(AppTheme.radiusMD),
        ),
      ),
    );
  }

  Widget _buildGridOverlay() {
    return CustomPaint(painter: GridPainter(), size: Size.infinite);
  }

  Widget _buildFocusIndicator() {
    if (_focusPoint == null) return const SizedBox.shrink();

    return Positioned(
      left: _focusPoint!.dx - 30,
      top: _focusPoint!.dy - 30,
      child: Transform.scale(
        scale: 1.0 - (_focusAnimation.value * 0.2),
        child: Opacity(
          opacity: 1.0 - _focusAnimation.value,
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              border: Border.all(color: AppTheme.brandPrimary, width: 2),
              borderRadius: BorderRadius.circular(AppTheme.radiusSM),
            ),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacing4),
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppTheme.textOnPrimaryColor,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(AppTheme.radiusXS),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCornerGuidelines() {
    return Positioned.fill(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacing24),
        child: Stack(
          children: [
            // Top Left
            Positioned(top: 0, left: 0, child: _buildCornerGuide()),
            // Top Right
            Positioned(
              top: 0,
              right: 0,
              child: Transform.rotate(
                angle: 1.5708, // 90 degrees
                child: _buildCornerGuide(),
              ),
            ),
            // Bottom Left
            Positioned(
              bottom: 0,
              left: 0,
              child: Transform.rotate(
                angle: -1.5708, // -90 degrees
                child: _buildCornerGuide(),
              ),
            ),
            // Bottom Right
            Positioned(
              bottom: 0,
              right: 0,
              child: Transform.rotate(
                angle: 3.14159, // 180 degrees
                child: _buildCornerGuide(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCornerGuide() {
    return Container(
      width: AppTheme.spacing32,
      height: AppTheme.spacing32,
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.8),
            width: 3,
          ),
          left: BorderSide(
            color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.8),
            width: 3,
          ),
        ),
      ),
    );
  }

  Widget _buildTopInfoBar() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + AppTheme.spacing16,
      left: AppTheme.spacing24,
      right: AppTheme.spacing24,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacing16,
          vertical: AppTheme.spacing12,
        ),
        decoration: BoxDecoration(
          color: AppTheme.glassColor,
          borderRadius: BorderRadius.circular(AppTheme.radiusLG),
          border: Border.all(color: AppTheme.glassBorderColor, width: 1),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryDarkColor.withValues(alpha: 0.3),
              blurRadius: AppTheme.shadowBlurMD,
              offset: const Offset(0, AppTheme.spacing4),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(
                  Icons.document_scanner_outlined,
                  color: AppTheme.textOnPrimaryColor,
                  size: AppTheme.spacing16,
                ),
                const SizedBox(width: AppTheme.spacing8),
                Text(
                  'Document Mode',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textOnPrimaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            GestureDetector(
              onTap: widget.onAutoCapture,
              child: Container(
                padding: const EdgeInsets.all(AppTheme.spacing6),
                decoration: BoxDecoration(
                  color:
                      widget.isAutoCapturing
                          ? AppTheme.brandPrimary.withValues(alpha: 0.3)
                          : Colors.transparent,
                  borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                ),
                child: Icon(
                  widget.isAutoCapturing
                      ? Icons.auto_awesome_outlined
                      : Icons.auto_awesome,
                  color:
                      widget.isAutoCapturing
                          ? AppTheme.brandPrimary
                          : AppTheme.textOnPrimaryColor.withValues(alpha: 0.7),
                  size: AppTheme.spacing16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = AppTheme.textOnPrimaryColor.withValues(alpha: 0.3)
          ..strokeWidth = 1;

    // Vertical lines
    final verticalSpacing = size.width / 3;
    for (int i = 1; i < 3; i++) {
      final x = verticalSpacing * i;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Horizontal lines
    final horizontalSpacing = size.height / 3;
    for (int i = 1; i < 3; i++) {
      final y = horizontalSpacing * i;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
